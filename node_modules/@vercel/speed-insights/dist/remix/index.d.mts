interface SpeedInsightsProps {
    dsn?: string;
    sampleRate?: number;
    route?: string | null;
    beforeSend?: BeforeSendMiddleware;
    debug?: boolean;
    scriptSrc?: string;
    endpoint?: string;
}
type EventTypes = 'vital';
interface Event {
    type: EventTypes;
    url: string;
}
type BeforeSendMiddleware = (data: Event) => Event | null | undefined | false;
interface Functions {
    beforeSend?: BeforeSendMiddleware;
}
interface SpeedInsights$1<T extends keyof Functions = keyof Functions> {
    queue: [T, Functions[T]][];
    addAction: (action: T, data: Functions[T]) => void;
}
declare global {
    interface Window {
        /** Base interface to track events */
        si?: SpeedInsights$1['addAction'];
        /** Queue for speed insights datapoints, before the library is loaded */
        siq?: SpeedInsights$1['queue'];
        sil?: boolean;
        /** used by Astro component only */
        speedInsightsBeforeSend?: BeforeSendMiddleware;
    }
}

declare function SpeedInsights(props: Omit<SpeedInsightsProps, 'route'>): JSX.Element;

export { SpeedInsights };

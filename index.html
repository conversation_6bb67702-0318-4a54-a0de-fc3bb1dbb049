<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="images/favicon2.ico" type="image/x-icon">
    <title><PERSON></title>
    <link href="style.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
</head>
<body>
    <header class="header">
        <a href="#" class="logo"><span style="font-family: Brush Script MT, Brush Script Std, cursive;">D</span>B</a>

        <nav class="navbar">
            <a href="#about-content">About Me</a>
            <a href="#experience">Experience</a>
            <a href="#education">Education</a>
            <a href="pages/projects.html">Projects (WIP)</a>
        </nav>
        <div class="menu-toggle">☰</div>
        <a href="mailto:<EMAIL>" class="email">Email Me</a>
    </header>

    <section class="home">
        <div class="home-content">
            <h3>Hi</h3>

            <h1>I'm <span>Daniel</span> Bajenov<br> a Software Development Student</h1>

            <div class="introduction">
                <p>Welcome to my portfolio, take a look at <a href="#about-content">about me</a>, my <a href="#experience">
                    experiences and opportunities</a> and a little bit about my <a href="#education">education</a>.</p>
            </div>

            <div class="btn-box">
                <button onclick="location.href='#about-content'" class="btn-1">About Me &dArr;</button>
                <button onclick="window.open('https://drive.google.com/file/d/1vvZyDsBYL1pmsrkniIqiKlUT2CMhT1KM/view?usp=sharing')"  class="btn-2">Resume</button>
            </div>
        </div>
        <div class="home-img">
            <img src="images/Me.JPG" alt="">
        </div>
    </section>

    <section style="scroll-margin-top: 300px;" class="about">

        <div id="about-content" class="about-content">
            <h2 >From Sheridan College <span>Daniel Bajenov</span></h2>
            <h3>A Little <span>About</span> Me</h3>
            <p>Hey! I'm currently studying Software Development and Networking Engineering at
                Sheridan College in Oakville, Ontario. I'm passionate about technology and programming,
                and I'm always looking for new opportunities to learn and grow. I love to develop front
                end applications but I dabble in back end development as well. I love everything about cars,
                gym and video games. Feel free to shoot me an email about anything!
            </p>
                <a onclick="location.href='#experience'" class="btn-2">Experience &dArr;</a>
        </div>
    </section>

    <section id="experience" class="experience">
        <div class="experience-content">
            <h1>Experience</h1>
            <h3>Languages & Skills:</h3>
            <div class="skills-container">
                <div class="skill-card">
                    <i class="fab fa-python"></i>
                    <h4>Python</h4>
                    <p>Advanced</p>
                    <!-- <span class="projects">5 Projects</span> -->
                </div>
                <div class="skill-card">
                    <i class="fab fa-java"></i>
                    <h4>Java</h4>
                    <p>Intermediate</p>
                    <!-- <span class="projects">3 Projects</span> -->
                </div>
                <div class="skill-card">
                    <i class="fab fa-js"></i>
                    <h4>JavaScript</h4>
                    <p>Intermediate</p>
                    <!-- <span class="projects">7 Projects</span> -->
                </div>
                <div class="skill-card">
                    <i class="fab fa-html5"></i>
                    <h4>HTML</h4>
                    <p>Advanced</p>
                    <!-- <span class="projects">8 Projects</span> -->
                </div>
                <div class="skill-card">
                    <i class="fab fa-css3-alt"></i>
                    <h4>CSS</h4>
                    <p>Advanced</p>
                    <!-- <span class="projects">8 Projects</span> -->
                </div>
                <div class="skill-card">
                    <i class="fas fa-database"></i>
                    <h4>SQL</h4>
                    <p>Beginner/Intermediate</p>
                    <!-- <span class="projects">4 Projects</span> -->
                </div>
            </div>
            <br>
            <h3>Frameworks & Libraries:</h3>
            <div class="skills-container">
                <div class="skill-card">
                    <i class="fa-brands fa-angular"></i>
                    <h4>Angular</h4>
                    <p>Advanced</p>
                    <!-- <span class="projects">5 Projects</span> -->
                </div>
                <div class="skill-card">
                    <i class="fa-brands fa-node-js"></i>
                    <h4>Node.js</h4>
                    <p>Intermediate</p>
                    <!-- <span class="projects">3 Projects</span> -->
                </div>
                <div class="skill-card">
                    <i class="fa-brands fa-react"></i>
                    <h4>ReactJS</h4>
                    <p>Beginner</p>
                    <!-- <span class="projects">3 Projects</span> -->
                </div>
                
            </div>
        </div>
    </section>
    <section id="education" class="education">
        <div class="education-content">
            <h1>Education</h1>
            <h3>Software <span>Development</span> & Networking <span>Engineering</span></h3>
            <p>Currently pursuing a diploma in 
                <a href="https://www.sheridancollege.ca/programs/computer-systems-technology-software-development-and-network-engineering" target="_blank" >
                    Software Development and Networking Engineering
                </a> 
                at Sheridan College, I am deeply passionate about technology and innovation. My coursework and projects 
                have equipped me with a strong foundation in both front-end and back-end development, enabling me to 
                build functional, efficient, and user-friendly applications. Beyond the classroom, I immerse myself in 
                exploring new programming tools, frameworks, and technologies to stay at the forefront of the industry.
                <br>Some of my courses include:</p>
                <li class="course-list">
                    <ul>Python & Java Object Oriented Programming</ul>
                    <ul>Web Development & Programming</ul>
                    <ul>Database Design & Managment</ul>
                    <ul>Cloud Enabled Networks & Network Security</ul>
                    <ul>Fundamentals of Software Design</ul>
                    <ul>Operating Systems (Linux)</ul>
                    <ul>Mobile Web-Based Applications</ul>
                    <ul>Ai and Machine Learning - Python</ul>
                </li>
        </div>

    </section>
    <footer class="footer">
        <div class="social">
            <a href="https://www.facebook.com/profile.php?id=100012384737897" target="_blank"><i class="fab fa-facebook"></i></a>
            <a href="https://www.linkedin.com/in/danielbajenov/" target="_blank"><i class="fab fa-linkedin"></i></a>
            <a href="https://github.com/DanjoBanjo03"><i class="fa-brands fa-github"></i></a>
        </div>

        <ul class="list">
            <li>
                <a href="#experience">Experience & Skills</a>
            </li>
            <li>
                <a href="#about-content">About Me</a>
            </li>
            <li>
                <a href="mailto:<EMAIL>">Email Me</a>
            </li>
        </ul>

        <p class="copyright">&copy; 2024 Daniel Bajenov | All Rights Reserved</p>
    </footer>
    <script defer src="/_vercel/speed-insights/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.querySelector('.menu-toggle');
            const navbar = document.querySelector('.navbar');
    
            menuToggle.addEventListener('click', function() {
                navbar.classList.toggle('active');
            });
    
            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                const isClickInside = navbar.contains(event.target) || menuToggle.contains(event.target);
                if (!isClickInside && navbar.classList.contains('active')) {
                    navbar.classList.remove('active');
                }
            });
    
            // Close menu when clicking on a link
            navbar.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', () => {
                    navbar.classList.remove('active');
                });
            });
        });
    </script>
</body>
</html>
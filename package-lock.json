{"name": "PortfolioHTML", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"@vercel/speed-insights": "^1.1.0"}}, "node_modules/@vercel/speed-insights": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@vercel/speed-insights/-/speed-insights-1.1.0.tgz", "integrity": "sha512-rAXxuhhO4mlRGC9noa5F7HLMtGg8YF1zAN6Pjd1Ny4pII4cerhtwSG4vympbCl+pWkH7nBS9kVXRD4FAn54dlg==", "hasInstallScript": true, "license": "Apache-2.0", "peerDependencies": {"@sveltejs/kit": "^1 || ^2", "next": ">= 13", "react": "^18 || ^19 || ^19.0.0-rc", "svelte": ">= 4", "vue": "^3", "vue-router": "^4"}, "peerDependenciesMeta": {"@sveltejs/kit": {"optional": true}, "next": {"optional": true}, "react": {"optional": true}, "svelte": {"optional": true}, "vue": {"optional": true}, "vue-router": {"optional": true}}}}}
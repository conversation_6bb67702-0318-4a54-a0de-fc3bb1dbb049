/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: rgba(0, 0, 0, 0.8);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px  100px 20px 135px;
    margin-bottom: 40px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: white;
    text-decoration: none;
    transition: 0.3s;
}

.logo:hover {
    color: #1abc9c;
}

.mainLogo {
    font-size: 40px;
    color: white;
    font-weight: 600;
    text-decoration: none;
    transition: 0.3s ease;
    font-family: Brush Script MT, Brush Script Std, cursive;
}

.mainLogo {
    font-size: 40px;
    color: white;
    font-weight: 600;
    transition: 0.3s ease;
    font-family: Brush Script MT, Brush Script Std, cursive;
}

.mainLogo span {
    color: #1abc9c;
}

.mainLogo:hover {
    color: #1abc9c;
    text-shadow: 0 0 25px #1abc9c, 0 0 50px #1abc9c;
    transform: scale(1.1);
}

nav ul {
    display: flex;
    list-style: none;
    font-size: 18px;
    color: white;
    font-weight: 500;
    margin: 0 20px;
    border-bottom: 3px solid transparent;
    transition: 0.3s ease;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    text-decoration: none;
    color: white;
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: #1abc9c;
    border-bottom: 3px solid #1abc9c;
}

/* Page title */
.page-title {
    text-align: center;
    margin-bottom: 40px;
}

.page-title h1 {
    font-size: 36px;
    margin-bottom: 10px;
    color: #222;
}

.page-title p {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Project grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

/* Project cards */
.project-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s, box-shadow 0.3s;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}

.project-image {
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-content {
    padding: 20px;
}

.project-title {
    font-size: 20px;
    margin-bottom: 10px;
    color: #222;
}

.project-description {
    color: #666;
    margin-bottom: 15px;
    font-size: 15px;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.tag {
    background-color: #f0f4f8;
    color: #1abc9c;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.project-link {
    display: inline-block;
    color: #1abc9c;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s;
}

.project-link:hover {
    color: #004999;
}

/* Project sizes */
.project-small {
    grid-column: span 1;
}

.project-medium {
    grid-column: span 2;
}

.project-large {
    grid-column: span 3;
}

/* Footer */
footer {
    background-color: #222;
    color: #fff;
    padding: 40px 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-logo {
    font-size: 20px;
    font-weight: 700;
}

.footer-links ul {
    display: flex;
    list-style: none;
}

.footer-links ul li {
    margin-left: 20px;
}

.footer-links ul li a {
    color: #ddd;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links ul li a:hover {
    color: #1abc9c;
}

.copyright {
    text-align: center;
    margin-top: 20px;
    color: #aaa;
    font-size: 14px;
}

html {
    scroll-behavior: smooth;
}

/* Responsive styles */
@media (max-width: 992px) {
    .project-medium, .project-large {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    nav ul {
        margin-top: 15px;
    }
    
    nav ul li {
        margin: 0 10px;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-links ul {
        margin-top: 15px;
        justify-content: center;
    }
    
    .footer-links ul li {
        margin: 0 10px;
    }
    
    .project-medium, .project-large {
        grid-column: span 1;
    }
}

@media (max-width: 576px) {
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .page-title h1 {
        font-size: 28px;
    }
    
    .page-title p {
        font-size: 16px;
    }
}

::-webkit-scrollbar{
    width: 15px;
}
::-webkit-scrollbar-thumb{
    background-color: #1abc9c;
}
::-webkit-scrollbar-track{
    background-color: rgba(0, 0, 0, 0.6);
    width: 50px;
}
/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #eaece9;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 15px 5% 15px 40px;
    background: rgba(220, 224, 219, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    padding-right: 40px;
    border-bottom: 1px solid rgba(55, 65, 81, 0.2);
}

.logo {
    font-size: 30px;
    color: #374151;
    font-weight: 600;
    transition: 0.3s ease;
    font-family: Brush Script MT, Brush Script Std, cursive;
    text-decoration: none;
}

.logo:hover {
    color: #f59e0b;
    text-shadow: 0 0 25px rgba(245, 158, 11, 0.4);
    transform: scale(1.05);
}

span {
    color: #f59e0b;
}

.navbar {
    display: flex;
    align-items: center;
}

.navbar a {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
    margin: 0 15px;
    border-bottom: 2px solid transparent;
    transition: 0.3s ease;
    text-decoration: none;
}

.navbar a:hover,
.navbar a:active {
    color: #f59e0b;
    border-bottom: 2px solid #f59e0b;
}

.email {
    padding: 8px 20px;
    background-color: #f59e0b;
    color: white;
    border: 2px solid transparent;
    border-radius: 6px;
    font-size: 12px;
    letter-spacing: 1px;
    font-weight: 600;
    transition: 0.3s ease;
    text-decoration: none;
}

.email:hover {
    background-color: #d97706;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
    color: white;
    transform: translateY(-2px);
}

/* Main content */
.main-content {
    padding-top: 120px;
    background: #eaece9;
    min-height: 100vh;
}

/* Page title */
.page-title {
    text-align: center;
    margin-bottom: 60px;
    padding: 60px 5% 40px;
}

.page-title h2 {
    color: #f59e0b;
    font-size: 42px;
    margin-bottom: 10px;
}

.page-title h3 {
    font-size: 62px;
    color: #374151;
    line-height: 1.2;
    margin-bottom: 20px;
}

.page-title p {
    font-size: 18px;
    color: #374151;
    margin: 25px 0 30px;
    max-width: 600px;
    margin: 0 auto;
}

/* Project grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

/* Project cards */
.project-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s, box-shadow 0.3s;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}

.project-image {
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-content {
    padding: 20px;
}

.project-title {
    font-size: 20px;
    margin-bottom: 10px;
    color: #222;
}

.project-description {
    color: #666;
    margin-bottom: 15px;
    font-size: 15px;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.tag {
    background-color: #f0f4f8;
    color: #1abc9c;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.project-link {
    display: inline-block;
    color: #1abc9c;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s;
}

.project-link:hover {
    color: #004999;
}

/* Project sizes */
.project-small {
    grid-column: span 1;
}

.project-medium {
    grid-column: span 2;
}

.project-large {
    grid-column: span 3;
}

/* Footer */
.footer {
    background-color: #374151;
    color: white;
    padding: 20px 5%;
    text-align: center;
}

.footer .social {
    text-align: center;
    padding-bottom: 25px;
    color: white;
}

.footer .social a {
    font-size: 24px;
    color: white;
    border: 2px solid #f59e0b;
    width: 40px;
    line-height: 38px;
    display: inline-block;
    text-align: center;
    border-radius: 50%;
    margin: 0 8px;
    transition: 0.3s ease;
    text-decoration: none;
}

.footer .social a:hover {
    transform: scale(1.1) translateY(-3px);
    color: #f59e0b;
    border: 2px solid #f59e0b;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.footer ul {
    margin-top: 0;
    padding: 0;
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 0;
    text-align: center;
    list-style: none;
    display: flex;
    justify-content: center;
}

.footer ul li a {
    color: white;
    border-bottom: 3px solid transparent;
    transition: 0.3s ease;
    text-decoration: none;
}

.footer ul li a:hover {
    border-bottom: 3px solid #f59e0b;
    color: #f59e0b;
}

.footer ul li {
    display: inline-block;
    padding: 0 15px;
}

.footer .copyright {
    margin-top: 15px;
    text-align: center;
    font-size: 12px;
    color: #d1d5db;
}

html {
    scroll-behavior: smooth;
}

/* Responsive styles */
@media (max-width: 992px) {
    .project-medium, .project-large {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    nav ul {
        margin-top: 15px;
    }
    
    nav ul li {
        margin: 0 10px;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-links ul {
        margin-top: 15px;
        justify-content: center;
    }
    
    .footer-links ul li {
        margin: 0 10px;
    }
    
    .project-medium, .project-large {
        grid-column: span 1;
    }
}

@media (max-width: 576px) {
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .page-title h1 {
        font-size: 28px;
    }
    
    .page-title p {
        font-size: 16px;
    }
}

::-webkit-scrollbar{
    width: 15px;
}
::-webkit-scrollbar-thumb{
    background-color: #1abc9c;
}
::-webkit-scrollbar-track{
    background-color: rgba(0, 0, 0, 0.6);
    width: 50px;
}